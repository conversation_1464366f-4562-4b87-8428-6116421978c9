'use client';

import { SignedIn, SignedOut, SignInButton } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import Button from './button';

interface PlanButtonProps {
  planId: string;
  className?: string;
  variant?: 'primary' | 'secondary';
}

// Configuração de aparência reutilizável para o modal
const clerkAppearance = {
  elements: {
    modalContent: 'bg-gray-900 border border-gray-700',
    headerTitle: 'text-white',
    headerSubtitle: 'text-gray-300',
    socialButtonsBlockButton: 'bg-gray-800 border border-gray-600 text-white hover:bg-gray-700',
    formFieldInput: 'bg-gray-800 border border-gray-600 text-white',
    formButtonPrimary: 'bg-lime-400 hover:bg-lime-500 text-black',
    footerActionLink: 'text-lime-400 hover:text-lime-300',
    dividerLine: 'bg-gray-600',
    dividerText: 'text-gray-400'
  },
  variables: {
    colorPrimary: '#a3e635',
    colorText: '#ffffff',
    colorTextSecondary: '#d1d5db',
    colorBackground: '#111827',
    colorInputBackground: '#374151',
    colorInputText: '#ffffff'
  }
};

export default function PlanButton({
  planId,
  className = '',
  variant = 'primary'
}: PlanButtonProps) {
  const router = useRouter();

  const handlePlanSelection = () => {
    // Passa apenas o ID do plano - preços são validados no servidor
    router.push(`/checkout?plan=${planId}`);
  };

  return (
    <>
      <SignedOut>
        <SignInButton mode="modal" appearance={clerkAppearance}>
          <Button variant={variant} className={className}>
            Escolher este plano
          </Button>
        </SignInButton>
      </SignedOut>

      <SignedIn>
        <Button
          variant={variant}
          className={className}
          onClick={handlePlanSelection}
        >
          Escolher este plano
        </Button>
      </SignedIn>
    </>
  );
}