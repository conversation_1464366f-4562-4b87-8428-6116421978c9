'use client';

import Button from '@/components/ui/button';
import Title from '@/components/ui/title';
import { getPlanById } from '@/lib/plans';
import { TERMS_DATA, TermCategory } from '@/lib/terms-data';
import { useAuth } from '@clerk/nextjs';
import { redirect, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

type FormData = {
  whatsapp: string;
  selectedTerm?: string;
  customTerm?: string;
  selectedCategory?: TermCategory;
};

export default function CheckoutPage() {
  const { userId } = useAuth();
  const searchParams = useSearchParams();

  if (!userId) {
    redirect('/');
  }

  // Busca os dados do plano no servidor - preços seguros!
  const planId = searchParams.get('plan') || '';
  const planData = getPlanById(planId);

  // Se plano não existir, redireciona para home
  if (!planData) {
    redirect('/#planos');
  }

  // Estados para o formulário
  const [selectedTerms, setSelectedTerms] = useState<string[]>([]);
  const [isManualMode, setIsManualMode] = useState(false);
  const [currentCategory, setCurrentCategory] = useState<TermCategory | ''>('');

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    setValue
  } = useForm<FormData>({
    mode: 'onChange'
  });

  const whatsapp = watch('whatsapp');
  const selectedTerm = watch('selectedTerm');
  const customTerm = watch('customTerm');

  // Verificar se todos os termos foram preenchidos
  const allTermsSelected = selectedTerms.length === planData.termsCount;
  const canFinalizePurchase = allTermsSelected && whatsapp;

  // Adicionar termo à lista
  const addTerm = () => {
    if (selectedTerms.length >= planData.termsCount) return;

    const termToAdd = isManualMode ? customTerm : selectedTerm;

    if (!termToAdd || termToAdd.trim() === '') return;
    if (selectedTerms.includes(termToAdd)) return;

    setSelectedTerms([...selectedTerms, termToAdd]);

    // Limpar campos
    setCurrentCategory('');
    setValue('selectedTerm', '');
    setValue('customTerm', '');
  };

  // Remover termo da lista
  const removeTerm = (index: number) => {
    setSelectedTerms(selectedTerms.filter((_, i) => i !== index));
  };

  // Alternar modo manual
  const toggleManualMode = () => {
    setIsManualMode(!isManualMode);
    setCurrentCategory('');
    setValue('selectedTerm', '');
    setValue('customTerm', '');
  };

  // Submit do formulário
  const onSubmit = (data: FormData) => {
    if (!canFinalizePurchase) return;

    console.log('Dados do formulário:', {
      plano: planData,
      termos: selectedTerms,
      whatsapp: data.whatsapp
    });

    // Aqui você implementaria a integração com gateway de pagamento
    alert('Formulário enviado! Implementar integração de pagamento.');
  };

  return (
    <div className="min-h-screen bg-gray-950 text-white pt-24">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex items-center justify-between mb-8">
          <div>
            <Title size="xl" as="h1" className="mb-2">
              Checkout - Prospect Fácil
            </Title>
            <p className="text-gray-300">
              Finalize sua compra e comece a receber leads qualificados
            </p>
          </div>

          {/* <SignedIn>
            <UserButton
              appearance={{
                elements: {
                  avatarBox: 'w-10 h-10'
                }
              }}
            />
          </SignedIn> */}
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          <div className="space-y-6">
            <div className="bg-gray-800/50 rounded-xl p-6 border border-gray-700">
              <h2 className="text-2xl font-semibold mb-4 text-lime-400">
                Seu Plano Selecionado
              </h2>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Plano:</span>
                  <span className="font-semibold">{planData.name}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Termos inclusos:</span>
                  <span className="font-semibold">
                    {planData.termsCount} {planData.termsCount === 1 ? 'termo' : 'termos'}
                  </span>
                </div>

                <div className="border-t border-gray-600 pt-4">
                  <div className="flex justify-between items-center text-xl">
                    <span className="font-semibold">Total:</span>
                    <span className="font-bold text-lime-400">R$ {planData.price}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Benefícios dinâmicos baseados no plano */}
            <div className="bg-gray-800/50 rounded-xl p-6 border border-gray-700">
              <h3 className="text-lg font-semibold mb-4 text-cyan-400">
                ✅ O que você vai receber:
              </h3>

              <ul className="space-y-3 text-gray-300">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                  {planData.termsCount} {planData.termsCount === 1 ? 'planilha Excel' : 'planilhas Excel'} com dados organizados
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                  Nome, telefone e email dos prospects
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                  Localização e segmento de atuação
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                  Entrega em até 24 horas por email
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
                  Suporte via WhatsApp
                </li>
              </ul>
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-gray-800/50 rounded-xl p-6 border border-gray-700">
              <h2 className="text-2xl font-semibold mb-6 text-lime-400">
                Informações de Pagamento
              </h2>

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Seção de Termos */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <label className="block text-sm font-medium text-gray-300">
                      Seus termos de busca ({selectedTerms.length}/{planData.termsCount})
                    </label>

                    {selectedTerms.length < planData.termsCount && (
                      <span className="text-xs text-cyan-400">
                        Faltam {planData.termsCount - selectedTerms.length} {planData.termsCount - selectedTerms.length === 1 ? 'termo' : 'termos'}
                      </span>
                    )}
                  </div>

                  {/* Lista de termos selecionados */}
                  {selectedTerms.length > 0 && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-300 mb-2">Termos selecionados:</h4>
                      <div className="space-y-2">
                        {selectedTerms.map((term, index) => (
                          <div key={index} className="flex items-center justify-between bg-gray-700 rounded-lg px-3 py-2">
                            <span className="text-sm text-gray-200">{index + 1}. {term}</span>
                            <button
                              type="button"
                              onClick={() => removeTerm(index)}
                              className="text-red-400 hover:text-red-300 text-sm"
                            >
                              ✕
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Formulário de adição de termos (se ainda não completou) */}
                  {selectedTerms.length < planData.termsCount && (
                    <div className="space-y-4">
                      {!isManualMode ? (
                        <div className="space-y-3">
                          {/* Select de Categoria */}
                          <select
                            value={currentCategory}
                            onChange={(e) => {
                              setCurrentCategory(e.target.value as TermCategory);
                              setValue('selectedTerm', '');
                            }}
                            className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-lime-400"
                          >
                            <option value="">Selecione uma categoria</option>
                            {Object.keys(TERMS_DATA).map((category) => (
                              <option key={category} value={category}>
                                {category}
                              </option>
                            ))}
                          </select>

                          {/* Select de Termo */}
                          {currentCategory && (
                            <select
                              {...register('selectedTerm')}
                              value={selectedTerm || ''}
                              className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-lime-400"
                            >
                              <option value="">Selecione um termo</option>
                              {TERMS_DATA[currentCategory].map((term) => (
                                <option key={term} value={term}>
                                  {term}
                                </option>
                              ))}
                            </select>
                          )}
                        </div>
                      ) : (
                        <input
                          {...register('customTerm')}
                          type="text"
                          placeholder="Digite seu termo personalizado"
                          className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-lime-400"
                        />
                      )}

                      <div className="flex gap-3">
                        <button
                          type="button"
                          onClick={toggleManualMode}
                          className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-500 rounded-lg text-sm transition-colors"
                        >
                          {isManualMode ? 'Voltar ao Select' : 'Digitar Termo Manualmente'}
                        </button>

                        <button
                          type="button"
                          onClick={addTerm}
                          disabled={!((isManualMode && customTerm) || (!isManualMode && selectedTerm))}
                          className="px-6 py-2 bg-lime-600 hover:bg-lime-500 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg text-sm font-medium transition-colors"
                        >
                          Adicionar
                        </button>
                      </div>
                    </div>
                  )}

                  {allTermsSelected && (
                    <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-3">
                      <p className="text-sm text-green-400">
                        ✅ Todos os termos foram selecionados!
                      </p>
                    </div>
                  )}
                </div>

                {/* Campo WhatsApp */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    WhatsApp para contato *
                  </label>
                  <input
                    {...register('whatsapp', {
                      required: 'WhatsApp é obrigatório',
                      pattern: {
                        value: /^\(\d{2}\)\s\d{4,5}-\d{4}$/,
                        message: 'Formato: (11) 99999-9999'
                      }
                    })}
                    type="tel"
                    placeholder="(11) 99999-9999"
                    className={`w-full px-4 py-3 bg-gray-700 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-lime-400 ${
                      errors.whatsapp ? 'border-red-500' : 'border-gray-600'
                    }`}
                  />
                  {errors.whatsapp && (
                    <p className="text-red-400 text-sm mt-1">{errors.whatsapp.message}</p>
                  )}
                </div>

                {/* Botão de Finalizar Compra */}
                <Button
                  type="submit"
                  className="w-full"
                  size="lg"
                  disabled={!canFinalizePurchase}
                >
                  {canFinalizePurchase
                    ? `Finalizar Compra - R$ ${planData.price}`
                    : `Complete os dados para finalizar (${planData.termsCount - selectedTerms.length} termos restantes)`
                  }
                </Button>

                <p className="text-xs text-gray-400 text-center">
                  Pagamento seguro via PIX, cartão ou boleto
                </p>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}