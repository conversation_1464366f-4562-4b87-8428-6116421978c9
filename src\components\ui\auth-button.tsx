'use client';

import { SignedIn, SignedOut, SignInButton } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import Button from './button';

interface AuthButtonProps {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  href?: string;
  className?: string;
  redirectToCheckout?: boolean;
}

// Configuração de aparência reutilizável
const clerkAppearance = {
  elements: {
    modalContent: 'bg-gray-900 border border-gray-700',
    headerTitle: 'text-white',
    headerSubtitle: 'text-gray-300',
    socialButtonsBlockButton: 'bg-gray-800 border border-gray-600 text-white hover:bg-gray-700',
    formFieldInput: 'bg-gray-800 border border-gray-600 text-white',
    formButtonPrimary: 'bg-lime-400 hover:bg-lime-500 text-black',
    footerActionLink: 'text-lime-400 hover:text-lime-300',
    dividerLine: 'bg-gray-600',
    dividerText: 'text-gray-400'
  },
  variables: {
    colorPrimary: '#a3e635', // lime-400
    colorText: '#ffffff',
    colorTextSecondary: '#d1d5db', // gray-300
    colorBackground: '#111827', // gray-900
    colorInputBackground: '#374151', // gray-700
    colorInputText: '#ffffff'
  }
};

export default function AuthButton({
  variant = 'primary',
  size = 'md',
  children,
  href = '#planos',
  className = '',
  redirectToCheckout = false
}: AuthButtonProps) {
  const router = useRouter();

  const handleAuthenticatedClick = () => {
    if (redirectToCheckout) {
      // Redireciona para a página de checkout
      router.push('/checkout');
    } else {
      // Comportamento padrão - scroll para seção
      const targetElement = document.getElementById(href.replace('#', ''));
      if (targetElement) {
        targetElement.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  return (
    <>
      <SignedOut>
        <SignInButton mode="modal" appearance={clerkAppearance}>
          <Button variant={variant} size={size} className={className}>
            {children}
          </Button>
        </SignInButton>
      </SignedOut>

      <SignedIn>
        <Button
          variant={variant}
          size={size}
          className={className}
          onClick={handleAuthenticatedClick}
        >
          {children}
        </Button>
      </SignedIn>
    </>
  );
}