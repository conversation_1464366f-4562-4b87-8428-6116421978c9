'use client';

import { useEffect, useState } from 'react';
import Button from './button';

export default function CookieBanner() {
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // Verifica se o usuário já fez uma escolha sobre cookies
    const cookieConsent = localStorage.getItem('cookieConsent');
    if (!cookieConsent) {
      setIsVisible(true);
    }
  }, []);

  const acceptAllCookies = () => {
    localStorage.setItem('cookieConsent', JSON.stringify({
      necessary: true,
      analytics: true,
      marketing: true,
      timestamp: new Date().toISOString()
    }));
    setIsVisible(false);
  };

  const acceptOnlyNecessary = () => {
    localStorage.setItem('cookieConsent', JSON.stringify({
      necessary: true,
      analytics: false,
      marketing: false,
      timestamp: new Date().toISOString()
    }));
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <>

      <div className="fixed inset-0 bg-black/70 z-50" onClick={() => setIsVisible(false)} />

      {/* Modal */}
      <div className="fixed bottom-4 left-4 right-4 sm:left-auto sm:bottom-6 sm:right-6 sm:max-w-3xl bg-white rounded-lg shadow-2xl z-50 border border-gray-200">
        <div className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-lime-400 rounded-full flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-black" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                🍪 Este site usa cookies
              </h3>
            </div>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>

          <p className="text-gray-600 text-sm mb-4 leading-relaxed">
            Utilizamos cookies para melhorar sua experiência, personalizar conteúdo e analisar nosso tráfego.
            Ao continuar navegando, você concorda com nossa política de cookies.
          </p>

          {showDetails && (
            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">🔒 Cookies Necessários</span>
                  <span className="text-green-600 font-medium">Sempre ativo</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">📊 Cookies Analíticos</span>
                  <span className="text-blue-600">Recomendado</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">🎯 Cookies de Marketing</span>
                  <span className="text-purple-600">Opcional</span>
                </div>
              </div>
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-8">
            <Button
              variant="primary"
              size="sm"
              onClick={acceptAllCookies}
              className="flex-1 !justify-center"
            >
              <span className="mr-0">Aceitar Todos</span>
              <svg className="hidden" />
            </Button>

            <Button
              variant="secondary"
              size="sm"
              onClick={acceptOnlyNecessary}
              className="flex-1 !justify-center !text-gray-700 !border-gray-400 hover:!text-gray-900 hover:!border-gray-600"
            >
              <span className="mr-2">Apenas Necessários</span>
            </Button>
          </div>

          <button
            onClick={() => setShowDetails(!showDetails)}
            className="w-full mt-3 text-xs text-gray-500 hover:text-gray-700 transition-colors"
          >
            {showDetails ? 'Ocultar detalhes' : 'Ver detalhes dos cookies'}
          </button>
        </div>
      </div>
    </>
  );
}