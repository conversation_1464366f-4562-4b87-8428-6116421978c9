@import 'tailwindcss';

:root {
  --background: #172224;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #172224;
    --foreground: #ededed;
  }
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  scroll-behavior: smooth;
}


[id] {
  scroll-margin-top: 40px;
}

/* Personalização adicional do Clerk */
.cl-modal {
  backdrop-filter: blur(8px);
}

.cl-modalContent {
  border-radius: 12px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.cl-socialButtonsBlockButton {
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

.cl-socialButtonsBlockButton:hover {
  transform: translateY(-1px) !important;
}

.cl-formButtonPrimary {
  border-radius: 8px !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

.cl-formButtonPrimary:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(163, 230, 53, 0.4) !important;
}

.cl-formFieldInput {
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

.cl-formFieldInput:focus {
  box-shadow: 0 0 0 3px rgba(163, 230, 53, 0.1) !important;
}
