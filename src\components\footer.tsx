import Image from 'next/image';
import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="bg-gray-950 text-white py-8 px-4 border-t border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
          {/* Logo */}
          <div className="flex justify-center md:justify-start">
            <Image
              src="/prospect-facil.svg"
              alt="Prospect Fácil"
              width={180}
              height={54}
              className="w-auto"
            />
          </div>

          {/* Direitos reservados e links legais */}
          <div className="text-center space-y-2">
            <p className="text-gray-400 text-sm">
              © 2025 Prospect Fácil. Todos os direitos reservados.
            </p>
            <div className="flex justify-center space-x-4">
              <Link
                prefetch
                href="/politica-privacidade"
                className="text-gray-400 hover:text-lime-400 transition-colors text-sm"
              >
                Política de Privacidade
              </Link>
            </div>
          </div>

          {/* Informações de contato */}
          <div className="text-center md:text-right space-y-2">
            <div className="flex items-center justify-center md:justify-end">
              <svg className="w-4 h-4 mr-2 text-lime-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-lime-400 transition-colors text-sm">
                <EMAIL>
              </a>
            </div>

            <div className="flex items-center justify-center md:justify-end">
              <svg className="w-4 h-4 mr-2 text-lime-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              <a href="tel:+5511999999999" className="text-gray-300 hover:text-lime-400 transition-colors text-sm">
                (11) 99999-9999
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}