import Footer from '@/components/footer';
import Header from '@/components/header';
import CookieBanner from '@/components/ui/cookie-banner';
import { ClerkProvider } from '@clerk/nextjs';
import type { Metadata } from 'next';
import { <PERSON>ei<PERSON>, <PERSON>eist_Mon<PERSON> } from 'next/font/google';
import './globals.css';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Prospect Fácil',
  description: 'Nunca foi tão fácil conseguir uma planilha de leads com qualidade.',
  keywords: ['leads', 'planilha', 'leads de qualidade', 'leads de vendas', 'leads de marketing', 'leads de vendas online', 'leads de marketing online', 'leads de vendas offline', 'leads de marketing offline', 'leads de vendas online', 'leads de marketing online', 'leads de vendas offline', 'leads de marketing offline', 'prospectar leads', 'prospectar leads de qualidade', 'prospectar leads de vendas', 'prospectar leads de marketing', 'prospectar leads de vendas online', 'prospectar leads de marketing online', 'prospectar leads de vendas offline', 'prospectar leads de marketing offline', 'prospectar leads de vendas online', 'prospectar leads de marketing online', 'prospectar leads de vendas offline', 'prospectar leads de marketing offline', 'prospectar leads de vendas online', 'prospectar leads de marketing online', 'prospectar leads de vendas offline', 'prospectar leads de marketing offline', 'prospectar leads de vendas online', 'prospectar leads de marketing online', 'prospectar leads de vendas offline', 'prospectar leads de marketing offline', 'prospectar leads de vendas online', 'prospectar leads de marketing online', 'prospectar leads de vendas offline', 'prospectar leads de marketing offline', 'prospectar leads de vendas online', 'prospectar leads de marketing online', 'prospectar leads de vendas offline', 'prospectar leads de marketing offline', 'prospectar leads de vendas online', 'prospectar leads de marketing online', 'prospectar leads de vendas offline', 'prospectar leads de marketing offline', 'prospectar leads de vendas online', 'prospectar leads de marketing online', 'prospectar leads de vendas offline', 'prospectar leads de marketing offline', 'prospectar leads de vendas online', 'prospectar leads de marketing online', 'prospectar leads de vendas offline', 'prospectar leads de marketing offline', 'prospectar leads de vendas online', 'prospectar leads de marketing online', 'prospectar leads de vendas offline', 'prospectar leads de marketing offline', 'prospectar leads de vendas online', 'prospectar leads de marketing online', 'prospectar leads de vendas offline', 'prospectar leads de marketing offline', 'prospectar leads de vendas online', 'prospectar leads de marketing online', 'prospectar leads de vendas offline', 'prospectar leads de marketing offline', 'prospectar leads de vendas online', 'prospectar leads de marketing online', 'prospectar leads de vendas offline'],
  icons: {
    icon: '/icon.svg',
    shortcut: '/icon.svg',
    apple: '/icon.svg',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider
      localization={{
        socialButtonsBlockButton: 'Continuar com {{provider|titleize}}',
        dividerText: 'ou',
        formFieldAction__forgotPassword: 'Esqueceu sua senha?',
        formFieldInputPlaceholder__emailAddress: 'Digite seu email',
        formFieldInputPlaceholder__password: 'Digite sua senha',
        formFieldLabel__emailAddress: 'Endereço de email',
        formFieldLabel__password: 'Senha',
        formButtonPrimary: 'Continuar',
        backButton: 'Voltar',
        //footerActionText: 'Não tem nenhum desses?',
        //footerActionLink: 'Obter ajuda',
        footerActionLink__useAnotherMethod: 'Usar outro método',
        signIn: {
          start: {
            title: 'Login no {{applicationName}}',
            subtitle: 'Bem-vindo! Faça login para continuar',
            actionText: '',
            actionLink: '',
          },
          emailCode: {
            title: 'Verifique seu email, digite o código de verificação',
            subtitle: '',
            formTitle: 'Código de verificação',
            resendButton: 'Não recebeu o código? Reenviar',
          },
          alternativeMethods: {
            title: 'Usar outro método',
            subtitle: 'Enfrentando problemas? Você pode usar qualquer um desses métodos para entrar.',
            actionText: 'Não tem nenhum desses?',
            actionLink: 'Obter ajuda',
            blockButton__emailCode: 'Código por email',
            blockButton__emailLink: 'Link por email',
            blockButton__password: 'Senha',
            blockButton__phoneCode: 'Código por SMS',
            blockButton__totp: 'Aplicativo de autenticação',
            blockButton__backupCode: 'Código de backup',
          },
        },
        signUp: {
          start: {
            title: 'Criar sua conta',
            subtitle: 'Bem-vindo! Por favor, preencha os detalhes para começar',
            actionText: 'Já tem uma conta?',
            actionLink: 'Entrar',
          },
        },
        userButton: {
          action__manageAccount: 'Gerenciar conta',
          action__signOut: 'Sair',
        },
      }}
      appearance={{
        variables: {
          colorPrimary: '#a3e635', // lime-400
          colorBackground: '#1f2937', // gray-800
          colorInputBackground: '#374151', // gray-700
          colorInputText: '#ffffff',
          colorText: '#ffffff',
          colorTextSecondary: '#d1d5db', // gray-300
          colorTextOnPrimaryBackground: '#000000',
          colorSuccess: '#22c55e',
          colorDanger: '#ef4444',
          colorWarning: '#f59e0b',
          colorNeutral: '#6b7280',
          borderRadius: '0.5rem',
          spacingUnit: '1rem',
          fontFamily: 'var(--font-geist-sans)',
          fontSize: '14px',
          fontWeight: {
            normal: 400,
            medium: 500,
            semibold: 600,
            bold: 700,
          }
        },
        elements: {
          modalContent: 'bg-white border border-gray-700',
          modalCloseButton: 'text-gray-400 hover:text-white',
          card: 'bg-gray-800 border border-gray-700',
          headerTitle: 'text-white',
          headerSubtitle: 'text-gray-300',
          socialButtonsBlockButton: {
            backgroundColor: 'transparent',
            border: '2px solid white',
            color: 'white',
            fontWeight: '500',
            '&:hover': {
              color: '#000',
              border: '2px solid white',
            },
          },
          socialButtonsBlockButtonText: {
            color: 'white',
            fontWeight: '500',
          },
          formButtonPrimary: 'bg-lime-600 hover:bg-lime-500 text-black font-semibold',
          formFieldInput: 'bg-gray-700 border border-gray-600 text-white placeholder:text-gray-400',
          formFieldLabel: 'text-gray-300',
          identityPreviewText: 'text-gray-300',
          identityPreviewEditButton: 'text-lime-400 hover:text-lime-300',
          footerActionText: 'text-gray-300',
          footerActionLink: 'text-lime-400 hover:text-lime-300',
          dividerText: 'text-gray-400',
          dividerLine: 'bg-gray-600',
          formHeaderTitle: 'text-white',
          formHeaderSubtitle: 'text-gray-300',
          badge: 'bg-gray-700 text-gray-300',
          alert: 'bg-red-900/20 border border-red-500/30 text-red-200',
          alertText: 'text-red-200',
          spinner: 'text-lime-400',
          accordionTriggerButton: 'text-gray-300 hover:text-white',
          accordionContent: 'text-gray-300',
          selectSearchInput: 'bg-gray-700 border border-gray-600 text-white',
          selectOption: 'text-gray-300 hover:bg-gray-600',
          breadcrumbsItem: 'text-gray-300',
          breadcrumbsItemDivider: 'text-gray-500',
          navbarButton: 'text-gray-300 hover:text-white',
          navbarMobileMenuButton: 'text-gray-300',
          pageScrollBox: 'bg-gray-800',
          otpCodeFieldInput: 'bg-gray-700 border-2 border-white text-white',
          formResendCodeLink: 'text-lime-400 hover:text-lime-300',
          fileDropAreaBox: 'border-gray-600 bg-gray-700/50',
          fileDropAreaButtonPrimary: 'bg-lime-600 hover:bg-lime-500 text-black',
          fileDropAreaIcon: 'text-gray-400',
          fileDropAreaText: 'text-gray-300',
          avatarBox: 'border-gray-600',
          userButtonBox: 'border-gray-600',
          userButtonTrigger: 'text-gray-300 hover:text-white',
          userButtonPopoverCard: 'bg-gray-800 border border-gray-700',
          userButtonPopoverActionButton: 'text-gray-300 hover:text-white hover:bg-gray-700',
          userButtonPopoverActionButtonText: 'text-gray-300',
          userButtonPopoverActionButtonIcon: 'text-gray-400',
          userButtonPopoverFooter: 'border-gray-600',
          menuButton: 'text-gray-300 hover:text-white hover:bg-gray-700',
          menuItem: 'text-gray-300 hover:text-white hover:bg-gray-700',
        },
        layout: {
          socialButtonsPlacement: 'top',
          showOptionalFields: true,
          logoImageUrl: '/prospect-facil.svg',
          logoLinkUrl: '/',
        },
        signIn: {
          elements: {
            rootBox: 'bg-gray-800',
            card: 'bg-gray-800 border border-gray-700 shadow-2xl',
          }
        },
        signUp: {
          elements: {
            rootBox: 'bg-gray-800',
            card: 'bg-gray-800 border border-gray-700 shadow-2xl',
          }
        },
        userProfile: {
          elements: {
            rootBox: 'bg-gray-800',
            card: 'bg-gray-800 border border-gray-700',
          }
        },
        organizationProfile: {
          elements: {
            rootBox: 'bg-gray-800',
            card: 'bg-gray-800 border border-gray-700',
          }
        }
      }}
    >
      <html lang="pt-BR">
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        >
          <Header />
          {children}
          <Footer />
          <CookieBanner />
        </body>
      </html>
    </ClerkProvider>
  );
}
