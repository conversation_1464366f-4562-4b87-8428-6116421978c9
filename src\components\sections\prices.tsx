import { getAllPlans } from '@/lib/plans';
import PlanButton from '../ui/plan-button';
import Title from '../ui/title';

export default function Prices() {
  // Busca os planos da fonte única da verdade
  const plans = getAllPlans();

  return (
    <section
      className="relative text-white py-20 px-4 overflow-hidden"
      id="planos"
      style={{
        backgroundImage: 'url(\'/bg-hero.webp\')',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Overlay para melhor legibilidade */}
      <div className="absolute inset-0 bg-gray-950/80"></div>

      <div className="relative max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-center text-center mb-16">
          <Title size="xl" as="h2" className="mb-6 max-w-5xl">
            Comece hoje e veja seus leads crescerem!
          </Title>
          <p className="text-gray-300 text-lg max-w-3xl leading-relaxed">
            Escolha um plano acessível repleto dos melhores recursos para
            engajar seu público, criar fidelidade do cliente e aumentar as vendas.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative rounded-2xl p-8 border-2 transition-all duration-300 ${
                plan.id === '2-termos'
                  ? 'bg-white text-gray-900 border-purple-500 scale-105 shadow-2xl'
                  : 'bg-gray-800/50 border-gray-700 hover:border-gray-600'
              }`}
            >
              <div className="text-center">
                <h3 className={`text-xl font-semibold mb-2 ${
                  plan.id === '2-termos' ? 'text-gray-900' : 'text-white'
                }`}>
                  {plan.name}
                </h3>

                <div className="mb-6">
                  <span className={`text-5xl font-bold ${
                    plan.id === '2-termos' ? 'text-gray-900' : 'text-white'
                  }`}>
                    R${plan.price}
                  </span>
                  <span className={`ml-2 text-sm ${
                    plan.id === '2-termos' ? 'text-gray-600' : 'text-gray-400'
                  }`}>
                    BRL
                  </span>
                  <p className={`text-sm mt-1 ${
                    plan.id === '2-termos' ? 'text-gray-600' : 'text-gray-400'
                  }`}>
                    {plan.description}
                  </p>
                </div>

                <PlanButton
                  planId={plan.id}
                  className={`w-full mb-8 ${
                    plan.id === '2-termos'
                      ? 'bg-purple-600 hover:bg-purple-700 text-white'
                      : ''
                  }`}
                />

                <div className="space-y-4">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center">
                      <svg
                        className={`w-5 h-5 mr-3 flex-shrink-0 ${
                          plan.id === '2-termos' ? 'text-green-600' : 'text-green-400'
                        }`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span className={`text-sm ${
                        plan.id === '2-termos' ? 'text-gray-700' : 'text-gray-300'
                      }`}>
                        {feature}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-gray-300 text-[18px] max-w-4xl mx-auto leading-relaxed mb-4 flex items-center flex-wrap justify-center gap-2">
            <svg className="w-7 h-7 text-green-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
            </svg>
            <strong>Pagamento somente via PIX</strong> - Rápido e seguro!
          </p>
          <p className="text-gray-300 mx-auto leading-relaxed text-2xl flex items-center flex-wrap justify-center gap-2">
            Para mais de 3 termos, entre em contato via
            <a
              href="https://wa.me/5511966749107"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 text-green-400 font-medium hover:text-green-300 transition-colors duration-200"
            >
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.55-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.087z"/>
              </svg>
              (11) 94779-7692
            </a>
          </p>
        </div>
      </div>
    </section>
  );
}
