// Fonte única da verdade para planos - NÃO pode ser alterada pelo usuário
export const PLANS = {
  '1-termo': {
    id: '1-termo',
    name: '1 Termo',
    price: '14,80',
    termsCount: 1,
    description: 'Pagamento único',
    features: [
      '1 termo de busca',
      'Suporte por WhatsApp',
    ],
  },
  '2-termos': {
    id: '2-termos',
    name: '2 Termos',
    price: '23,80',
    termsCount: 2,
    description: 'Pagamento único',
    features: [
      '2 termos de busca',
      'Suporte por WhatsApp',
    ],
  },
  '3-termos': {
    id: '3-termos',
    name: '3 Termos',
    price: '28,80',
    termsCount: 3,
    description: 'Pagamento único',
    features: [
      '3 termos de busca',
      'Suporte por WhatsApp',
    ],
  },
} as const;

export type PlanId = keyof typeof PLANS;

export function getPlanById(planId: string): typeof PLANS[PlanId] | null {
  return PLANS[planId as PlanId] || null;
}

export function getAllPlans() {
  return Object.values(PLANS);
}