{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "typescript.preferences.organizeImports": true, "typescript.format.semicolons": "insert", "typescript.format.insertSpaceAfterCommaDelimiter": true, "typescript.format.insertSpaceAfterKeywordsInControlFlowStatements": true, "typescript.format.insertSpaceBeforeAndAfterBinaryOperators": true, "typescript.preferences.quoteStyle": "single"}