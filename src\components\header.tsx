'use client';

import AuthButton from '@/components/ui/auth-button';
import { SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/nextjs';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

export default function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const menuItems = [
    { name: 'Como funciona', href: '#como-funciona' },
    { name: 'Planos', href: '#planos' },
    { name: 'FAQ', href: '#faq' },
    { name: 'Contato', href: '#contato' },
  ];

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const handleSmoothScroll = (event: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    event.preventDefault();

    const targetId = href.replace('#', '');
    const targetElement = document.getElementById(targetId);

    if (targetElement) {
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }

    // Fechar menu mobile se estiver aberto
    if (isMobileMenuOpen) {
      closeMobileMenu();
    }
  };

  return (
    <header className="fixed top-0 backdrop-blur-xs w-full z-50 shadow-sm border-b border-green-900 bg-[rgba(23,34,36,0.7)]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/">
              <Image
                src="/prospect-facil.svg"
                alt="Prospect Fácil"
                width={200}
                height={60}
                className="w-auto"
              />
            </Link>
          </div>

          {/* Menu de navegação */}
          <nav className="hidden md:block">
            <ul className="flex space-x-8">
              {menuItems.map((item, index) => (
                <li key={index}>
                  <a
                    href={item.href}
                    onClick={(e) => handleSmoothScroll(e, item.href)}
                    className="relative text-white hover:text-green-400 transition-colors duration-200 font-medium group cursor-pointer"
                  >
                    {item.name}
                    <span className="absolute left-0 -bottom-1 w-0 h-0.5 bg-gradient-to-r from-lime-400 to-green-400 transition-all duration-300 group-hover:w-full"></span>
                  </a>
                </li>
              ))}
            </ul>
          </nav>

          {/* Botões de autenticação e CTA */}
          <div className="hidden md:flex items-center space-x-4">
            <SignedOut>
              <SignInButton
                mode="modal"

              >
                <AuthButton size="sm" href="#planos" redirectToCheckout={true}>
              Fazer Login
                </AuthButton>
              </SignInButton>
            </SignedOut>

            <SignedIn>
              <UserButton
                appearance={{
                  elements: {
                    avatarBox: 'w-8 h-8'
                  }
                }}
              />
            </SignedIn>

          </div>

          {/* Menu mobile (hamburger) */}
          <div className="md:hidden">
            <button
              onClick={toggleMobileMenu}
              className="text-white hover:text-green-400 transition-colors duration-200"
            >
              {isMobileMenuOpen ? (
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Menu Mobile */}
        {isMobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-gray-900/95 backdrop-blur-sm rounded-lg mt-2">
              {menuItems.map((item, index) => (
                <a
                  key={index}
                  href={item.href}
                  onClick={(e) => handleSmoothScroll(e, item.href)}
                  className="block px-3 py-2 text-white hover:text-green-400 hover:bg-gray-800/50 rounded-md transition-colors duration-200 font-medium cursor-pointer"
                >
                  {item.name}
                </a>
              ))}

              {/* Autenticação mobile */}
              <div className="px-3 py-4 border-t border-gray-700 mt-2">
                <SignedOut>
                  <SignInButton
                    mode="modal"
                  >
                    <AuthButton size="md" href="#planos" redirectToCheckout={true} className='w-full'>
              Fazer Login
                    </AuthButton>
                  </SignInButton>
                </SignedOut>

                <SignedIn>
                  <div className="flex items-center space-x-3 py-2">
                    <UserButton
                      appearance={{
                        elements: {
                          avatarBox: 'w-8 h-8'
                        }
                      }}
                    />
                    <span className="text-white">Minha conta</span>
                  </div>
                </SignedIn>
              </div>

              {/* <div className="px-3 py-2">
                <AuthButton size="sm" className="w-full" href="#planos" redirectToCheckout={true}>
                  Quero Leads
                </AuthButton>
              </div> */}
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
