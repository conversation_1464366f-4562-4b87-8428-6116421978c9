import { FlatCompat } from "@eslint/eslintrc";
import { dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    rules: {
      // Detectar funções/variáveis não utilizadas
      "@typescript-eslint/no-unused-vars": "error",
      "no-unused-vars": "off", // Desabilita a regra padrão do JS para usar a do TypeScript
      
      // Detectar console.log
      "no-console": "warn",
      
      // Regras adicionais de boas práticas
      "prefer-const": "error",
      "no-var": "error",
      "no-debugger": "error",
      
      // Regras de formatação simples
      "indent": ["error", 2],
      "quotes": ["error", "single"],
      "semi": ["error", "always"],
      "no-trailing-spaces": "error",
      "no-multiple-empty-lines": ["error", { "max": 1 }],
    },
  },
];

export default eslintConfig;
