import Image from 'next/image';
import Title from '../ui/title';

export default function How() {
  return (
    <section className="bg-gray-950 text-white py-20 px-4 relative overflow-hidden" id="como-funciona">
      {/* Efeito de luz discreto no lado esquerdo */}
      <div className="absolute top-0 left-0 w-96 h-full pointer-events-none">
        <div className="w-full h-full bg-gradient-to-r from-cyan-500/15 via-cyan-400/10 to-transparent opacity-40"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="flex flex-col items-center justify-center text-center mb-16">
          <Title size="xl" as="h2" className="mb-6 max-w-3xl">
            Como funciona
          </Title>

          <p className="text-gray-300">
            Nossa ferramenta é fácil de usar e você pode começar a usar em poucos minutos.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Imagem */}
          <div className="relative">
            <div className="relative rounded-2xl overflow-hidden">
              <Image
                src="/img-how-1.png"
                alt="Como funciona"
                width={500}
                height={400}
                className="max-w-full"
              />
            </div>
            {/* Ícone da seta */}
            <div className="absolute -bottom-6 -right-6">
              <Image
                src="/icon-how-1.svg"
                alt="Seta"
                width={60}
                height={60}
                className="w-15 h-15"
              />
            </div>
          </div>

          {/* Lista de funcionalidades */}
          <div className="space-y-6">
            <div className="bg-gray-800/50 rounded-xl p-6 border border-gray-700">

              <h3 className="text-xl font-semibold text-cyan-400">
                1. Escolha seus termos
              </h3>

              <p className="text-gray-300 leading-relaxed">
                Defina os termos dos clientes que deseja prospectar. Exemplo: &quot;Dentista São Bernardo&quot; ou &quot;Advogados Curitiba&quot;. Cada termo é cobrado conforme nossos planos.
              </p>
            </div>

            <div className="bg-gray-800/50 rounded-xl p-6 border border-gray-700">

              <h3 className="text-xl font-semibold mb-3 text-green-400">
                2. Preencha o formulário
              </h3>
              <p className="text-gray-300 leading-relaxed">
                Informe seu nome, email, WhatsApp e os termos escolhidos. Nosso sistema processará sua solicitação de forma rápida e eficiente.
              </p>
            </div>

            <div className="bg-gray-800/50 rounded-xl p-6 border border-gray-700">

              <h3 className="text-xl font-semibold mb-3 text-lime-400">
                3. Receba suas planilhas
              </h3>
              <p className="text-gray-300 leading-relaxed">
                Em algumas horas, você receberá o link das planilhas Excel no seu email com todos os prospects encontrados, prontos para contato.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
