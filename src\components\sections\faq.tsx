'use client';

import { useState } from 'react';
import Title from '../ui/title';

export default function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const faqs = [
    {
      question: 'Como funciona a prospecção de leads?',
      answer: 'Você escolhe os termos dos clientes que deseja prospectar (ex: \'Dentista São Paulo\'), preenche o formulário com suas informações e em algumas horas recebe uma planilha Excel com todos os prospects encontrados, incluindo dados de contato e informações relevantes.'
    },
    {
      question: 'Quanto tempo leva para receber as planilhas?',
      answer: 'O tempo de entrega varia conforme o plano escolhido: Starter (24h), Growth (18h) e Scale (12h). Nosso sistema processa automaticamente sua solicitação e você recebe o link por email assim que estiver pronto.'
    },
    {
      question: 'Quantos termos posso usar por vez?',
      answer: 'Depende do seu plano: Starter permite 1 termo, Growth permite 3 termos e Scale permite até 5 termos por solicitação. Cada termo representa uma busca específica como \'Advogados Curitiba\' ou \'Dentistas São Bernardo\'.'
    },
    {
      question: 'Os dados dos prospects são válidos e atualizados?',
      answer: 'Sim! Nosso sistema coleta informações de fontes públicas e confiáveis. No plano Scale, oferecemos dados validados com verificação extra de qualidade para garantir maior precisão dos contatos.'
    },
    {
      question: 'Posso cancelar minha assinatura a qualquer momento?',
      answer: 'Claro! Não há fidelidade. Você pode cancelar sua assinatura a qualquer momento e continuar usando o serviço até o final do período pago. Sem taxas de cancelamento ou multas.'
    },
    {
      question: 'Que tipo de informações vem nas planilhas?',
      answer: 'As planilhas incluem: nome da empresa, telefone, email (quando disponível), endereço, site, redes sociais e outras informações relevantes do negócio. O plano Scale oferece dados mais detalhados e filtros avançados.'
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="bg-gray-50 py-20 px-4" id="faq">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-16">
          <Title size="xl" as="h2" className="mb-6">
            Dúvidas frequentes
          </Title>
        </div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="bg-white rounded-xl border border-gray-200 overflow-hidden transition-all duration-200 hover:shadow-md"
            >
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full px-6 py-5 text-left flex justify-between items-center focus:outline-none"
              >
                <span className="text-lg font-semibold text-gray-900 pr-4">
                  {faq.question}
                </span>
                <div className="flex-shrink-0">
                  <div
                    className={`w-6 h-6 flex items-center justify-center transition-transform duration-300 ${
                      openIndex === index ? 'rotate-45' : 'rotate-0'
                    }`}
                  >
                    <svg
                      className="w-6 h-6 text-gray-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                  </div>
                </div>
              </button>

              <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  openIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                }`}
              >
                <div className="px-6 pb-5">
                  <p className="text-gray-700 leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}