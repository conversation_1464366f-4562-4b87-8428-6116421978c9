import Button from '@/components/ui/button';
import Title from '@/components/ui/title';
import Image from 'next/image';

export default function Hero() {
  return (
    <section className="text-white pt-24 px-4 relative">
      {/* Imagem de fundo */}
      <div className="absolute top-0 right-0 w-[400px] h-[400px] sm:w-[1000px] sm:h-[600px]">
        <div
          className="w-full h-full bg-contain bg-no-repeat bg-right opacity-65"
          style={{
            backgroundImage: 'url(/bg-hero.webp)',
          }}
        />
      </div>

      <div className="absolute inset-0"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Bloco do texto - Esquerda */}
          <div className="space-y-8">

            <div className="inline-flex items-center bg-green-500/20 border border-green-500/30 rounded-full px-4 py-2">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
              <span className="text-green-400 text-sm font-medium">Planilhas Exclusivas</span>
            </div>

            {/* Título principal */}
            <div>
              <Title size="2xl" as="h1">
                Facilidade na <br /> Prospecção de Leads
              </Title>
            </div>

            {/* Subtítulo */}
            <p className="text-xl text-gray-200 leading-relaxed max-w-[500px]">
              Planilhas objetivas, com as informações necessárias para você
              prospectar de forma eficiente.
            </p>

            {/* Botões */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button variant="secondary" size="md">
                Ver demonstração
              </Button>
              <Button size="md">
                <a href="#planos">
                  Começar Agora
                </a>
              </Button>
            </div>
          </div>

          {/* Bloco da imagem - Direita */}
          <div className="relative flex justify-center lg:justify-end">
            <div className="relative w-full max-w-lg z-20">

              <div className="absolute top-1 -left-15 z-10">
                <Image
                  src="/img-hero-resul.png"
                  alt="Pessoa usando planilhas de prospecção"

                  width={300}
                  height={300}
                />
              </div>

              <div className="absolute bottom-10 left-1 z-10">
                <svg width="156" height="129" viewBox="0 0 156 129" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path opacity="0.5" d="M18.5939 48.5201C18.5474 48.6537 18.4973 48.7833 18.4543 48.9209C12.047 68.4245 23.2122 80.8029 36.7381 86.9276C45.5896 90.9373 56.8902 92.8196 69.3938 90.4346C70.2491 104.073 79.1859 114.738 89.7965 119.659C101.413 125.047 116.823 124.922 133.279 119.976C132.993 120.997 132.685 122.032 132.329 123.079C132.103 123.741 132.18 124.216 132.426 124.536C132.173 125.1 131.919 125.665 131.639 126.239C130.773 127.998 132.904 128.84 134.547 127.406C141.136 121.653 147.75 115.928 154.354 110.205C155.709 109.031 156.228 107.155 154.802 106.929C153.676 106.753 152.551 106.577 151.419 106.407C151.089 106.097 150.574 105.95 149.908 106.084C149.817 106.1 149.722 106.126 149.635 106.146C145.706 105.574 141.749 105.05 137.714 104.65C136.113 104.492 134.414 106.768 135.287 107.536C135.18 107.773 135.113 108.018 135.108 108.265C135.043 110.577 134.799 112.925 134.36 115.33C107.66 123.463 84.6622 117.327 76.923 98.2025C75.8911 95.6535 75.0722 92.5608 74.6994 89.1767C77.6042 88.3551 80.5562 87.3208 83.5563 85.9946C97.0287 80.0616 119.568 61.5485 109.433 51.7705C99.4457 42.1341 77.7352 59.499 72.0188 74.3782C70.4021 78.5873 69.5728 82.6408 69.3734 86.5057C48.5053 90.2347 30.0878 81.5076 23.4576 68.326C21.1087 63.6495 20.4564 57.2276 22.0893 50.4777C31.8883 55.4474 45.0132 57.7559 58.595 45.1895C71.4706 33.2712 68.5319 17.5787 56.1219 17.4978C42.4122 17.4152 27.0195 30.757 20.4301 44.1612C19.769 43.7277 19.1217 43.2965 18.4779 42.8694C7.15401 35.3621 -2.80956 21.5939 10.8605 1.77007C12.1771 -0.141728 9.63651 -0.624626 8.29155 1.25491C-5.46092 20.4516 1.46739 35.8124 12.2252 44.0876C14.028 45.4891 16.1762 47.0443 18.5939 48.5201ZM89.1915 57.7332C96.0536 52.9041 104.194 52.1847 106.513 57.1733C109.627 63.8548 98.3487 73.7148 90.7936 78.274C85.3491 81.5596 79.8646 83.8666 74.4814 85.3426C74.4645 76.1791 77.9929 65.6178 89.1915 57.7332ZM36.2005 29.2105C49.5024 18.6311 67.0292 19.1185 62.0056 34.6673C58.7806 44.6412 46.4629 50.5173 38.1462 50.5561C32.367 50.581 27.668 48.5447 23.4748 46.0717C25.7722 40.1591 29.8824 34.2331 36.2005 29.2105Z" fill="#9ae600"/>
                </svg>
              </div>
              <Image
                src="/pessoa-1.webp"
                alt="Pessoa usando planilhas de prospecção"
                className="w-full h-auto object-contain"
                width={500}
                height={500}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}