type TitleProps = {
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

export default function Title({
  children,
  size = 'lg',
  className = '',
  as: Component = 'h1'
}: TitleProps) {
  const sizes = {
    sm: 'text-xl sm:text-2xl',
    md: 'text-2xl sm:text-3xl',
    lg: 'text-3xl sm:text-4xl',
    xl: 'text-4xl sm:text-5xl',
    '2xl': 'text-5xl sm:text-6xl',
    '3xl': 'text-6xl sm:text-7xl',
  };

  const baseStyles = 'font-bold bg-gradient-to-r from-lime-400 via-green-400 to-cyan-400 bg-clip-text text-transparent';

  return (
    <Component className={`${baseStyles} ${sizes[size]} ${className}`}>
      {children}
    </Component>
  );
}