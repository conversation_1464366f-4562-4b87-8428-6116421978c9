Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000210059F6C, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (0002102860C8, 0007FFFFB5F8, 0007FFFFB740, 0002102684E0) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068EFF (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28EFF
0007FFFFBA20  00021006A225 (000000004000, 000000000000, 000000000008, 000000000008) msys-2.0.dll+0x2A225
0007FFFFBA20  00021006A4A9 (000000000008, 0007FFFFBAC8, 0007FFFFBCA4, 0000FFFFFFFF) msys-2.0.dll+0x2A4A9
0007FFFFBA20  000210193F2B (000000000008, 0007FFFFBAC8, 0007FFFFBCA4, 0000FFFFFFFF) msys-2.0.dll+0x153F2B
0007FFFFBA20  00010042DB65 (000A00000004, 0007FFFFBCB4, 00010040DD62, 000A00094B00) bash.exe+0x2DB65
0000FFFFFFFF  00010043C4F8 (0000000000C2, 000A00000000, 000A00095270, 000A00086180) bash.exe+0x3C4F8
000000000070  00010043E6BE (000A000951C0, 000A00000001, 000210178DD2, 0007FFFFBCB0) bash.exe+0x3E6BE
000000000070  000100441B06 (000700000001, 000000000000, 0007FFFFBDA0, 000000000000) bash.exe+0x41B06
000000000070  000100441D36 (000A00000000, 000000000000, 000000000000, 000000000000) bash.exe+0x41D36
000000000019  0001004449D8 (000000000000, 000000000010, 000210268720, 000A000950D0) bash.exe+0x449D8
000000000019  00010043D697 (000000000010, 000000000000, 000210193F2B, 000A00095050) bash.exe+0x3D697
00000000000E  00010043DC63 (000A00095070, 00000000002D, 000A00093490, 000A000933E0) bash.exe+0x3DC63
0001004F6F60  000100433786 (000000094F00, 000000000001, 000000000000, 000000000000) bash.exe+0x33786
000000000001  0001004440DE (0001004F896E, 000000000000, 000210178DD2, 000A00000000) bash.exe+0x440DE
0000FFFFFFFF  000100419C92 (000210193F2B, 0000FFFFFFFF, 000000000001, 000A00094FB0) bash.exe+0x19C92
000A00094F00  00010041AC6A (000210178E7F, 000000000000, 0001004EB4A0, 000100000000) bash.exe+0x1AC6A
000A00094F00  00010041C50F (000210193F2B, 000A00000000, 000A001BCE30, 000A00094F00) bash.exe+0x1C50F
0000FFFFFFFF  0001004179C7 (000210193F2B, 000210268720, 000A001BD130, 000A00094F00) bash.exe+0x179C7
0000000001AE  00010041AC6A (000A001BD170, 000210268720, 000000000001, 000000000001) bash.exe+0x1AC6A
0000000001AE  00010041813E (0007FFFFC610, 000A00028320, 0002100AC638, 000A00094D40) bash.exe+0x1813E
000000000001  00010046F3A5 (000A0017ADE0, 000000000414, 0000CE000000, 0000000000BE) bash.exe+0x6F3A5
000A000283D0  00010046E127 (000000000009, 000A00021C40, 000000000000, 0007FFFFCBEF) bash.exe+0x6E127
000000000000  00010046E2D5 (0002102ADAC0, 0002102686E0, 000000000000, 000000000005) bash.exe+0x6E2D5
000000000000  0001004EA48C (000A00001EF0, 000A00000160, 0002100C96E9, 000000000000) bash.exe+0xEA48C
0007FFFFCD30  000210047F01 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x7F01
000000000000  000210045AC3 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x5AC3
0007FFFFFFF0  000210045B74 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x5B74
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD39E90000 ntdll.dll
7FFD380D0000 KERNEL32.DLL
7FFD37820000 KERNELBASE.dll
7FFD39280000 USER32.dll
7FFD37740000 win32u.dll
7FFD39080000 GDI32.dll
7FFD37620000 gdi32full.dll
7FFD37D60000 msvcp_win.dll
7FFD37520000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD39470000 advapi32.dll
7FFD37FD0000 msvcrt.dll
7FFD390B0000 sechost.dll
7FFD39150000 RPCRT4.dll
7FFD377F0000 bcrypt.dll
7FFD36DD0000 CRYPTBASE.DLL
7FFD37CD0000 bcryptPrimitives.dll
7FFD39420000 IMM32.DLL
