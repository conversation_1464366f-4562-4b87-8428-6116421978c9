'use client';

import Image from 'next/image';
import { useState } from 'react';
import Button from '../ui/button';

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    whatsapp: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Aqui você pode adicionar a lógica de envio do formulário

  };

  return (
    <section className="py-20 px-4" id="contato">
      <div className="max-w-7xl mx-auto">
        <div className="rounded-3xl pt-5 pl-10 pr-10 relative overflow-hidden border-2 border-green-400">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Formulário */}
            <div className="space-y-6">
              <div>
                <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
                  Deixe sua mensagem
                </h2>
                <p className="text-gray-300 text-lg">
                  Nós entraremos em contato com você!
                </p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input
                    type="text"
                    name="name"
                    placeholder="Nome"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 rounded-xl bg-white border border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent"
                    required
                  />
                  <input
                    type="email"
                    name="email"
                    placeholder="E-mail"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 rounded-xl bg-white border border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input
                    type="tel"
                    name="whatsapp"
                    placeholder="WhatsApp"
                    value={formData.whatsapp}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 rounded-xl bg-white border border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent"
                    required
                  />
                  <div></div>
                </div>

                <textarea
                  name="message"
                  placeholder="Mensagem"
                  rows={4}
                  value={formData.message}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 rounded-xl bg-white border border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent resize-none"
                  required
                />

                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                  <Button>
                    Enviar mensagem
                  </Button>

                  <p className="text-gray-400 text-sm max-w-xs">
                    Ao clicar no botão &quot;Enviar mensagem&quot; você concorda com o envio e processamento de seus dados pessoais
                  </p>
                </div>
              </form>
            </div>

            {/* Imagem */}
            <div className="relative">
              <div className="relative">
                <Image
                  src="/img-contato-1.png"
                  alt="Atendente"
                  width={500}
                  height={500}
                  className="w-full h-auto max-w-lg mx-auto"
                />

                {/* Badge flutuante */}
                <div className="absolute top-8 right-8 bg-lime-400 text-gray-900 px-4 py-2 rounded-full text-sm font-semibold flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-600 rounded-full animate-pulse"></div>
                  Potencialize Seu Negócio
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}